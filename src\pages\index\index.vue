<template>
  <view class="home-container">
    <!-- 顶部标题栏 -->
    <view class="navbar">
      <text class="navbar-title"></text>
      <view class="navbar-icons">
        <text class="iconfont icon-more"></text>
        <text class="iconfont icon-camera"></text>
      </view>
    </view>

    <!-- 医院横幅 -->
    <image class="banner-img" src="https://www.jmsrmyy.cn/fzlm/sydt/202310/W020240318595492533018.png" mode="aspectFill" width="100%"/>


    <!-- 搜索框 -->
    <view class="search-bar">
      <text class="iconfont icon-search"></text>
      <input class="search-input" placeholder="搜索科室、医生" />
      <text class="search-btn">搜索</text>
    </view>

    <!-- 便民服务 -->
    <view class="section-title">
      <text class="section-title-main">便民服务</text>
    </view>
    <view class="convenience-guides">
      <view class="guide-btn">
        <view class="guide-icon">
          <text class="iconfont icon-play"></text>
        </view>
        <text class="guide-text">挂号操作指引</text>
      </view>
      <view class="guide-btn">
        <view class="guide-icon">
          <text class="iconfont icon-play"></text>
        </view>
        <text class="guide-text">支付操作指引</text>
      </view>
    </view>
    <view class="convenience-cards">
      <view class="convenience-card">
        <view class="convenience-card-icon" style="background:#ff7f7f;">
          <text class="iconfont icon-book"></text>
        </view>
        <view class="convenience-card-info">
          <text class="convenience-card-title">发热门诊</text>
          <text class="convenience-card-desc">专家团队，在线答疑</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="convenience-card">
        <view class="convenience-card-icon" style="background:#b39ddb;">
          <text class="iconfont icon-calendar"></text>
        </view>
        <view class="convenience-card-info">
          <text class="convenience-card-title">方舱医院住院申请</text>
          <text class="convenience-card-desc">新冠患者请进入填写申请资料</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>

    <!-- 互联网医院服务 -->
    <view class="section-title">
      <text class="section-title-main">互联网医院服务</text>
    </view>
    <view class="service-cards">
      <view class="service-card">
        <image class="service-icon" src="https://img.icons8.com/color/48/medical-mobile-app.png" />
        <view class="service-info">
          <text class="service-title">网上问诊续药</text>
          <text class="service-desc">病情咨询、常见病、慢性病在线复诊续方支持医保在线结算</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="service-card">
        <image class="service-icon" src="https://img.icons8.com/color/48/nurse-female.png" />
        <view class="service-info">
          <text class="service-title">专科护理门诊</text>
          <text class="service-desc">护理指导|康复指导|健康咨询</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>

    <!-- 专科门诊 -->
    <view class="section-title">
      <text class="section-title-main">专科门诊</text>
    </view>
    <view class="dept-grid">
      <view class="dept-item" v-for="item in deptList" :key="item.text">
        <image :src="item.icon" class="dept-icon" />
        <text class="dept-label">{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { isLoggedIn, safeNavigate } from '@/utils/auth';

const deptList = [
  { icon: 'https://img.icons8.com/color/48/thyroid-2.png', text: '内分泌科' },
  { icon: 'https://img.icons8.com/color/48/brain.png', text: '神内一区门诊' },
  { icon: 'https://img.icons8.com/color/48/medical-doctor.png', text: '全科医学科' },
  { icon: 'https://img.icons8.com/color/48/kidney.png', text: '泌尿外科' },
  { icon: 'https://img.icons8.com/color/48/heart-with-pulse.png', text: '心血管科门诊' },
  { icon: 'https://img.icons8.com/color/48/female.png', text: '妇科' },
  { icon: 'https://img.icons8.com/color/48/kidney.png', text: '肾内门诊' },
  { icon: 'https://img.icons8.com/color/48/pregnant.png', text: '产科' },
  { icon: 'https://img.icons8.com/color/48/lungs.png', text: '呼吸科' },
  { icon: 'https://img.icons8.com/color/48/more.png', text: '更多' },
];

// 检查登录状态
const checkLoginStatus = () => {
  console.log('首页检查登录状态...');

  if (!isLoggedIn()) {
    console.log('未检测到token，准备跳转到登录页');
    safeNavigate('/pages/login/login');
  } else {
    console.log('已登录，可以访问首页');
  }
};

// 页面加载时检查登录状态
onLoad(() => {
  console.log('首页onLoad触发');
  checkLoginStatus();
});
</script>

<style scoped>
.home-container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 60px;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 8px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.navbar-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
}
.navbar-icons {
  display: flex;
  gap: 12px;
}
.iconfont {
  font-size: 22px;
  color: #888;
}
.banner-img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  margin-bottom: 8px;
}
.search-bar {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  margin: 0 12px 12px 12px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 15px;
  margin-left: 8px;
}
.search-btn {
  color: #1abc9c;
  font-size: 15px;
  margin-left: 8px;
}
.section-title {
  margin: 16px 0 8px 16px;
  font-size: 18px;
  font-weight: bold;
  color: #222;
  display: flex;
  align-items: center;
}
.section-title-main {
  border-left: 4px solid #1abc9c;
  padding-left: 8px;
}
.service-cards {
  margin: 0 12px 0 12px;
}
.service-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 14px 16px;
  margin-bottom: 14px;
}
.service-icon {
  width: 44px;
  height: 44px;
  margin-right: 12px;
}
.service-info {
  flex: 1;
}
.service-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}
.service-desc {
  font-size: 13px;
  color: #888;
  margin-top: 2px;
}
.dept-grid {
  display: flex;
  flex-wrap: wrap;
  background: #f8fafb;
  border-radius: 12px;
  margin: 0 12px 0 12px;
  padding: 10px 0 10px 0;
}
.dept-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}
.dept-icon {
  width: 36px;
  height: 36px;
  margin-bottom: 4px;
}
.dept-label {
  font-size: 13px;
  color: #333;
}
.convenience-guides {
  display: flex;
  justify-content: flex-start;
  gap: 24px;
  margin: 0 0 12px 16px;
}
.guide-btn {
  display: flex;
  align-items: center;
  background: #e3f2fd;
  border-radius: 8px;
  padding: 6px 14px 6px 8px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.guide-icon {
  width: 28px;
  height: 28px;
  background: #42a5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}
.guide-text {
  font-size: 15px;
  color: #222;
}
.convenience-cards {
  margin: 0 12px 0 12px;
}
.convenience-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 14px 16px;
  margin-bottom: 14px;
}
.convenience-card-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.convenience-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}
.convenience-card-desc {
  font-size: 13px;
  color: #888;
  margin-top: 2px;
}
</style>

<template>
  <view class="poster-container">
    <view class="poster-content">
      <!-- 左侧分类菜单 -->
      <view class="category-sidebar">
        <view 
          v-for="(category, index) in categories" 
          :key="category.id"
          :class="['category-item', activeCategory === index ? 'active' : '']"
          @click="switchCategory(index)"
        >
          <text class="category-text">{{ category.name }}</text>
        </view>
      </view>

      <!-- 右侧海报展示区域 -->
      <view class="poster-main">
        <scroll-view class="poster-scroll" scroll-y>
          <view v-if="currentPosters.length > 0" class="poster-grid">
            <view
              v-for="poster in currentPosters"
              :key="poster.id"
              class="poster-item"
              @click="previewPoster(poster)"
            >
              <image
                :src="poster.image"
                class="poster-image"
                mode="aspectFill"
              />
              <view class="poster-title">{{ poster.title }}</view>
            </view>
          </view>
          <view v-else class="empty-state">
            <text class="empty-text">暂无海报数据</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 分类数据
const categories = ref([
  { id: 1, name: '活动海报' },
  { id: 2, name: '新品海报' },
  { id: 3, name: '年度销冠' },
  { id: 4, name: '当季产品' },
  { id: 5, name: '198套餐' },
  { id: 6, name: '生活用纸' },
  { id: 7, name: '个护清洁' },
  { id: 8, name: '美妆护肤' },
  { id: 9, name: '日化清洁' },
  { id: 10, name: '服饰套装' },
  { id: 11, name: '箱包皮具' },
  { id: 12, name: '精选好茶' },
  { id: 13, name: '居家生活' },
  { id: 14, name: '产品标牌' },
  { id: 15, name: '其他好物' }
])

const activeCategory = ref(0)

// 海报数据
const posterData = ref({
  0: [ // 活动海报
    {
      id: 1,
      title: '红包中国年',
      image: 'https://picsum.photos/400/600?random=1'
    },
    {
      id: 2,
      title: '39元新人礼',
      image: 'https://picsum.photos/400/600?random=2'
    },
    {
      id: 3,
      title: '春节特惠',
      image: 'https://picsum.photos/400/600?random=3'
    },
    {
      id: 4,
      title: '限时抢购',
      image: 'https://picsum.photos/400/600?random=4'
    }
  ],
  1: [ // 新品海报
    {
      id: 5,
      title: '新品上市',
      image: 'https://picsum.photos/400/600?random=5'
    },
    {
      id: 6,
      title: '限时特惠',
      image: 'https://picsum.photos/400/600?random=6'
    },
    {
      id: 7,
      title: '新品推荐',
      image: 'https://picsum.photos/400/600?random=7'
    },
    {
      id: 8,
      title: '热销新品',
      image: 'https://picsum.photos/400/600?random=8'
    }
  ],
  2: [ // 年度销冠
    {
      id: 9,
      title: '年度销冠产品',
      image: 'https://picsum.photos/400/600?random=9'
    },
    {
      id: 10,
      title: '销量冠军',
      image: 'https://picsum.photos/400/600?random=10'
    }
  ],
  3: [ // 当季产品
    {
      id: 11,
      title: '春季新品',
      image: 'https://picsum.photos/400/600?random=11'
    },
    {
      id: 12,
      title: '夏季热销',
      image: 'https://picsum.photos/400/600?random=12'
    }
  ],
  4: [ // 198套餐
    {
      id: 13,
      title: '198超值套餐',
      image: 'https://picsum.photos/400/600?random=13'
    },
    {
      id: 14,
      title: '优惠套餐',
      image: 'https://picsum.photos/400/600?random=14'
    }
  ],
  5: [ // 生活用纸
    {
      id: 15,
      title: '优质纸巾',
      image: 'https://picsum.photos/400/600?random=15'
    },
    {
      id: 16,
      title: '家庭装纸巾',
      image: 'https://picsum.photos/400/600?random=16'
    }
  ],
  6: [ // 个护清洁
    {
      id: 17,
      title: '洗护套装',
      image: 'https://picsum.photos/400/600?random=17'
    },
    {
      id: 18,
      title: '个人护理',
      image: 'https://picsum.photos/400/600?random=18'
    }
  ],
  7: [ // 美妆护肤
    {
      id: 19,
      title: '护肤精华',
      image: 'https://picsum.photos/400/600?random=19'
    },
    {
      id: 20,
      title: '美妆套装',
      image: 'https://picsum.photos/400/600?random=20'
    }
  ],
  8: [ // 日化清洁
    {
      id: 21,
      title: '清洁用品',
      image: 'https://picsum.photos/400/600?random=21'
    },
    {
      id: 22,
      title: '家居清洁',
      image: 'https://picsum.photos/400/600?random=22'
    }
  ],
  9: [ // 服饰套装
    {
      id: 23,
      title: '时尚服装',
      image: 'https://picsum.photos/400/600?random=23'
    }
  ],
  10: [ // 箱包皮具
    {
      id: 24,
      title: '精品箱包',
      image: 'https://picsum.photos/400/600?random=24'
    }
  ],
  11: [ // 精选好茶
    {
      id: 25,
      title: '优质茶叶',
      image: 'https://picsum.photos/400/600?random=25'
    }
  ],
  12: [ // 居家生活
    {
      id: 26,
      title: '居家用品',
      image: 'https://picsum.photos/400/600?random=26'
    }
  ],
  13: [ // 产品标牌
    {
      id: 27,
      title: '产品标识',
      image: 'https://picsum.photos/400/600?random=27'
    }
  ],
  14: [ // 其他好物
    {
      id: 28,
      title: '精选好物',
      image: 'https://picsum.photos/400/600?random=28'
    }
  ]
})

// 当前显示的海报
const currentPosters = computed(() => {
  return posterData.value[activeCategory.value as keyof typeof posterData.value] || []
})

// 切换分类
const switchCategory = (index: number) => {
  activeCategory.value = index
}

// 预览海报
const previewPoster = (poster: any) => {
  uni.previewImage({
    urls: [poster.image],
    current: poster.image
  })
}
</script>

<style scoped>
.poster-container {
  height: 100vh;
  background: #f5f5f5;
}

.poster-content {
  display: flex;
  height: 100%;
}

.category-sidebar {
  width: 100px;
  background: #f5f5f5;
  border-right: 1px solid #e5e5e5;
}

.category-item {
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e8e8e8;
  background: #f5f5f5;
  position: relative;
  cursor: pointer;
}

.category-item.active {
  background: #fff;
  color: #333;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #ff6b35;
}

.category-text {
  font-size: 13px;
  color: #666;
  text-align: center;
  padding: 0 8px;
}

.category-item.active .category-text {
  color: #333;
  font-weight: 500;
}

.poster-main {
  flex: 1;
  background: #f5f5f5;
}

.poster-scroll {
  height: 100%;
}

.poster-grid {
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.poster-item {
  width: calc(50% - 4px);
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.poster-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.poster-image {
  width: 100%;
  height: 180px;
}

.poster-title {
  padding: 8px;
  font-size: 12px;
  color: #333;
  text-align: center;
  background: #fff;
  line-height: 1.2;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}
</style>

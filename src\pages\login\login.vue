<template>
  <view class="login-container">
    <view class="login-card">
      <view class="login-form">
        <view class="input-group">
          <i class="iconfont icon-user"></i>
          <input type="text" placeholder="User name / Email" v-model="username" />
        </view>
        <view class="input-group">
          <i class="iconfont icon-lock"></i>
          <input type="password" placeholder="Password" v-model="password" />
        </view>
        <button class="login-btn" type="button" @click="goToIndex">
          LOG IN NOW <span class="arrow">&gt;</span>
        </button>
      </view>
      <view class="social-login">
  <text>log in via</text>
        <view class="icons">
          <i class="iconfont icon-instagram"></i>
          <i class="iconfont icon-facebook"></i>
          <i class="iconfont icon-twitter"></i>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const username = ref('');
const password = ref('');

const goToIndex = () => {
  // 简单验证
  if (!username.value.trim()) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    });
    return;
  }

  if (!password.value.trim()) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    });
    return;
  }

  // 模拟登录成功，设置token
  const token = 'demo_access_token_' + Date.now();

  try {
    uni.setStorageSync('access_token', token);
    console.log('Token已设置:', token);

    uni.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    });

    // 延迟跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }, 1000);
  } catch (error) {
    console.error('登录失败:', error);
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'error'
    });
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #b7aaff;
}
.login-card {
  width: 340px;
  background: linear-gradient(135deg, #fff 60%, #7b5cff 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  padding: 40px 30px 30px 30px;
  position: relative;
  overflow: hidden;
}
.login-card::before {
  content: '';
  position: absolute;
  top: -60px;
  right: -60px;
  width: 180px;
  height: 180px;
  background: #7b5cff33;
  border-radius: 50%;
  z-index: 0;
}
.login-form {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.input-group {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #b7aaff;
  padding-bottom: 6px;
}
.input-group i {
  color: #7b5cff;
  margin-right: 8px;
  font-size: 18px;
}
.input-group input {
  border: none;
  outline: none;
  background: transparent;
  flex: 1;
  font-size: 15px;
  color: #333;
  padding: 6px 0;
}
.login-btn {
  margin-top: 18px;
  width: 100%;
  background: #fff;
  color: #7b5cff;
  border: 2px solid #7b5cff;
  border-radius: 24px;
  padding: 10px 0;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.login-btn:hover {
  background: #7b5cff;
  color: #fff;
}
.arrow {
  font-size: 18px;
}
.social-login {
  margin-top: 32px;
  text-align: right;
  z-index: 1;
}
.social-login text {
  color: #888;
  font-size: 13px;
  margin-right: 8px;
}
.social-login .icons {
  display: inline-flex;
  gap: 12px;
  vertical-align: middle;
}
.social-login i {
  font-size: 18px;
  color: #7b5cff;
  cursor: pointer;
  transition: color 0.2s;
}
.social-login i:hover {
  color: #4e2cff;
}
</style> 
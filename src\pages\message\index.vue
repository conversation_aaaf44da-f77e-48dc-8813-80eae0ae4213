<template>
  <view class="message-container">
    <view class="header">
      <view class="header-title">消息中心</view>
      <view class="header-icons">
        <text class="iconfont icon-more"></text>
        <text class="iconfont icon-scan"></text>
      </view>
    </view>
    <view class="msg-list">
      <view class="msg-item">
        <view class="msg-icon-wrap">
          <text class="iconfont icon-msg"></text>
        </view>
        <view class="msg-info">
          <view class="msg-title">系统消息</view>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 静态数据演示
</script>

<style scoped>
.message-container {
  min-height: 100vh;
  background: #f7f7f7;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 16px 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.header-title {
  font-size: 22px;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.header-icons {
  display: flex;
  gap: 16px;
}
.iconfont {
  font-size: 22px;
  color: #888;
}
.msg-list {
  margin: 18px 12px 0 12px;
}
.msg-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  padding: 14px 16px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.msg-icon-wrap {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: #1abc9c;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
}
.icon-msg {
  color: #fff;
  font-size: 24px;
}
.msg-info {
  flex: 1;
}
.msg-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}
</style> 
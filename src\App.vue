<script>
export default {
  onLaunch: function() {
    console.log('App onLaunch');

    // 检查是否有token（仅记录日志，不强制跳转）
    const token = uni.getStorageSync('access_token');
    console.log('App启动时检查token:', token ? '已登录' : '未登录');

    // 让各个页面自己处理登录检查，避免在App层面强制跳转
    if (!token) {
      console.log('App启动时未检测到token，由页面自行处理登录逻辑');
    }
  },
  onShow: function() {
    console.log('App onShow');
  },
  onHide: function() {
    console.log('App onHide');
  }
}
</script>

<style>
/*每个页面公共css */
</style>

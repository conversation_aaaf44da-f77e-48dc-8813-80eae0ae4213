<template>
  <view class="order-container">
    <view class="header">
      <view class="header-title">订单中心</view>
      <view class="header-icons">
        <text class="iconfont icon-more"></text>
        <text class="iconfont icon-grid"></text>
      </view>
    </view>
    <view class="order-tabs">
      <view v-for="(tab, idx) in tabs" :key="tab" :class="['tab-item', idx === activeTab ? 'active' : '']" @click="activeTab = idx">
        {{ tab }}
      </view>
    </view>
    <view class="order-content">
      <view v-if="activeTab === 0" class="empty-order">
        <image class="empty-img" src="https://img.icons8.com/ios/100/empty-box.png" />
        <view class="empty-title">暂无问诊订单</view>
        <view class="empty-desc">仅显示通过本小程序创建的订单</view>
      </view>
      <view v-else class="empty-order">
        <image class="empty-img" src="https://img.icons8.com/ios/100/empty-box.png" />
        <view class="empty-title">暂无相关订单</view>
        <view class="empty-desc">仅显示通过本小程序创建的订单</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const tabs = ['问诊订单', '药品&商品', '有号提醒&客服代挂'];
const activeTab = ref(0);
</script>

<style scoped>
.order-container {
  min-height: 100vh;
  background: #f7f7f7;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 16px 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.header-title {
  font-size: 22px;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.header-icons {
  display: flex;
  gap: 16px;
}
.iconfont {
  font-size: 22px;
  color: #888;
}
.order-tabs {
  display: flex;
  background: #fff;
  border-radius: 12px 12px 0 0;
  margin: 14px 12px 0 12px;
  padding: 0 0 0 8px;
  overflow-x: auto;
}
.tab-item {
  font-size: 16px;
  color: #333;
  padding: 14px 18px 10px 18px;
  margin-right: 8px;
  border-bottom: 2px solid transparent;
  cursor: pointer;
}
.tab-item.active {
  color: #1abc9c;
  font-weight: bold;
  border-bottom: 2px solid #1abc9c;
}
.order-content {
  background: #fff;
  border-radius: 0 0 12px 12px;
  margin: 0 12px 0 12px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-order {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 48px;
}
.empty-img {
  width: 80px;
  height: 80px;
  margin-bottom: 18px;
}
.empty-title {
  font-size: 18px;
  color: #888;
  margin-bottom: 8px;
}
.empty-desc {
  font-size: 14px;
  color: #bbb;
}
</style> 
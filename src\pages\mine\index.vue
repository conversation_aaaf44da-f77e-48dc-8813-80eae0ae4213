<template>
  <view class="mine-container">
    <view class="header">
      <view class="header-title">个人中心</view>
      <view class="header-icons">
        <text class="iconfont icon-more"></text>
        <text class="iconfont icon-scan"></text>
      </view>
    </view>
    <view class="user-card">
      <image class="avatar" src="https://img.icons8.com/color/96/user-male-circle.png" />
      <view class="user-info">
        <view class="user-phone">180****3419</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    <view class="quick-entry">
      <view class="entry-item">
        <text class="iconfont icon-patient"></text>
        <view class="entry-label">就诊人</view>
      </view>
      <view class="entry-item">
        <text class="iconfont icon-star"></text>
        <view class="entry-label">我的关注</view>
      </view>
    </view>
    <view class="menu-list">
      <view class="menu-item">
        <text class="iconfont icon-order"></text>
        <view class="menu-text">订单中心</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" @click="goToPoster">
        <text class="iconfont icon-order"></text>
        <view class="menu-text">产品海报</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item">
        <text class="iconfont icon-remind"></text>
        <view class="menu-text">复诊提醒</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item">
        <text class="iconfont icon-address"></text>
        <view class="menu-text">收货地址</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item">
        <text class="iconfont icon-setting"></text>
        <view class="menu-text">设置</view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { safeNavigate } from '@/utils/auth';

// 跳转到产品海报页面
const goToPoster = () => {
  safeNavigate('/pages/poster/index');
}
</script>

<style scoped>
.mine-container {
  min-height: 100vh;
  background: #f7f7f7;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 16px 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.header-title {
  font-size: 22px;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.header-icons {
  display: flex;
  gap: 16px;
}
.iconfont {
  font-size: 22px;
  color: #888;
}
.user-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  margin: 18px 12px 0 12px;
  padding: 14px 16px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  margin-right: 14px;
}
.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}
.user-phone {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  flex: 1;
}
.quick-entry {
  display: flex;
  background: #fff;
  border-radius: 12px;
  margin: 14px 12px 0 12px;
  padding: 12px 0;
  justify-content: space-around;
}
.entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.entry-label {
  font-size: 15px;
  color: #333;
  margin-top: 4px;
}
.menu-list {
  margin: 18px 12px 0 12px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.menu-item {
  display: flex;
  align-items: center;
  padding: 18px 0 18px 18px;
  border-bottom: 1px solid #f0f0f0;
}
.menu-item:last-child {
  border-bottom: none;
}
.menu-text {
  font-size: 16px;
  color: #333;
  margin-left: 12px;
  flex: 1;
}
</style>
